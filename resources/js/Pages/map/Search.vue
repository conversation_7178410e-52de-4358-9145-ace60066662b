<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
    Search,
    Globe,
    Building,
    MapPin,
    Layers,
    Navigation,
    Crosshair,
    Loader2,
    AlertCircle,
    Clock,
    LocateFixed,
    Trash2
} from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout.vue';

// --- REACTIVE STATE ---
const searchQuery = ref('');
const searchMode = ref(localStorage.getItem('searchMode') || 'text'); // 'text' or 'coordinates'
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const showFilters = ref(false); // Hide filters by default
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);
const coordinateSearchResults = ref([]);
const lastSearchedCoords = ref(null);
const coordinateForm = ref({
    latitude: '',
    longitude: ''
});
const selectedLocationData = ref(null); // For displaying selected location details

// Recommended searches
const recommendedSearches = [
    'Umudugudu wa gatenga',
    'Akarere ka Gasabo',
    'Umurenge wa mushubati',
    'kk 8 Av',
    'Akagari ka Kicukiro',
    'Sector of Kagarama'
];

// Map related state
const mapContainer = ref(null);
const map = ref(null);
const currentMarker = ref(null);
const currentGeoJsonLayer = ref(null);
const mapTheme = ref('default');

// Rwanda bounds for map constraints
const RWANDA_BOUNDS = [
    [28.8617546, -2.9389804], // Southwest coordinates
    [30.8997343, -1.0474083]  // Northeast coordinates
];

// --- COMPUTED PROPERTIES ---
const canSearch = computed(() => {
    if (searchMode.value === 'coordinates') {
        const lat = parseFloat(coordinateForm.value.latitude);
        const lng = parseFloat(coordinateForm.value.longitude);
        // Check if coordinates are valid numbers and within Rwanda bounds
        if (!isNaN(lat) && !isNaN(lng)) {
            return isWithinRwandaBounds(lat, lng);
        }
        return false;
    }
    return searchQuery.value.trim().length >= 3;
});

// Rwanda bounds validation function
const isWithinRwandaBounds = (lat, lng) => {
    const [swLng, swLat] = RWANDA_BOUNDS[0];
    const [neLng, neLat] = RWANDA_BOUNDS[1];
    return lat >= swLat && lat <= neLat && lng >= swLng && lng <= neLng;
};

const hasResults = computed(() => {
    return Object.values(searchResults.value).some(array =>
        array && Array.isArray(array) && array.length > 0
    );
});

const totalResults = computed(() => {
    if (searchMode.value === 'coordinates') {
        return coordinateSearchResults.value?.length || 0;
    }
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const getPlaceholderText = computed(() => {
    const placeholders = {
        text: {
            rw: 'Shakisha ahantu mu Rwanda...',
            en: 'Search Rwanda locations...',
            fr: 'Rechercher des lieux au Rwanda...'
        },
        coordinates: {
            rw: 'Injiza latitude,longitude...',
            en: 'Enter latitude,longitude...',
            fr: 'Entrez latitude,longitude...'
        }
    };
    return placeholders[searchMode.value]?.[selectedLanguage.value] || 'Search...';
});

// Language and filter options
const languages = [
    { code: 'rw', name: 'Kinyarwanda', flag: '🇷🇼' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' }
];

const filters = [
    { value: 'all', label: 'All Locations' },
    { value: 'province', label: 'Provinces' },
    { value: 'district', label: 'Districts' },
    { value: 'sector', label: 'Sectors' },
    { value: 'cell', label: 'Cells' },
    { value: 'village', label: 'Villages' }
];

const mapThemes = [
    { value: 'default', label: 'Default' },
    { value: 'satellite', label: 'Satellite' }
];

// --- METHODS ---
const initializeMap = () => {
    if (!mapContainer.value) return;

    map.value = new maplibregl.Map({
        container: mapContainer.value,
        style: getMapStyle(),
        center: [29.8739, -1.9403], // Rwanda center
        zoom: 8,
        maxBounds: RWANDA_BOUNDS,
        minZoom: 6,
        maxZoom: 18,
        attributionControl: false,
        maxBoundsViscosity: 1.0
    });

    // Add navigation controls
    map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
    map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');

    // Add click handler for coordinate search
    map.value.on('click', handleMapClick);

    // Add load handler
    map.value.on('load', () => {
        console.log('Map loaded successfully');
    });
};

const getMapStyle = () => {
    const themes = {
        default: {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.2, 'raster-contrast': 0.1, 'raster-opacity': 0.9 }
        },
        satellite: {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles © Esri',
            paint: {}
        }
    };

    return {
        version: 8,
        sources: {
            osm: {
                type: 'raster',
                tiles: [themes[mapTheme.value]?.url || themes.default.url],
                tileSize: 256,
                attribution: themes[mapTheme.value]?.attribution || themes.default.attribution
            }
        },
        layers: [{
            id: 'osm-tiles',
            type: 'raster',
            source: 'osm',
            paint: themes[mapTheme.value]?.paint || themes.default.paint
        }],
    };
};

// Add click marker variable
let clickMarker = null;

const handleMapClick = (e) => {
    if (!map.value) return;

    const { lng, lat } = e.lngLat;

    // Check if coordinates are within Rwanda bounds
    if (!isWithinRwandaBounds(lat, lng)) {
        error.value = 'Please click within Rwanda boundaries';
        setTimeout(() => error.value = null, 3000);
        return;
    }

    // Place custom marker on map click in coordinate mode
    if (searchMode.value === 'coordinates') {
        coordinateForm.value.latitude = lat.toFixed(6);
        coordinateForm.value.longitude = lng.toFixed(6);

        if (clickMarker) clickMarker.remove();
        clickMarker = new maplibregl.Marker({ color: '#1A773E' })
            .setLngLat([lng, lat])
            .addTo(map.value);

        // Add hover text to click marker
        const markerElement = clickMarker.getElement();
        markerElement.title = `Clicked location: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;

        // Add hover effects without scaling
        markerElement.addEventListener('mouseenter', () => {
            markerElement.style.filter = 'brightness(1.2) drop-shadow(0 4px 8px rgba(26, 119, 62, 0.4))';
            markerElement.style.transition = 'filter 0.2s ease';
            markerElement.style.zIndex = '1000';
        });

        markerElement.addEventListener('mouseleave', () => {
            markerElement.style.filter = 'none';
            markerElement.style.zIndex = 'auto';
        });

        submitCoordinateSearch();
    }
};



const addMarker = (lat, lng, popup = null, hoverText = null) => {
    // Remove existing marker
    if (currentMarker.value) {
        currentMarker.value.remove();
    }

    // Create new marker
    currentMarker.value = new maplibregl.Marker({
        color: '#1A773E' // gorilla-primary color
    })
    .setLngLat([lng, lat])
    .addTo(map.value);

    if (popup) {
        currentMarker.value.setPopup(new maplibregl.Popup().setHTML(popup));
    }

    // Add hover tooltip functionality
    if (hoverText) {
        const markerElement = currentMarker.value.getElement();
        markerElement.title = hoverText; // Native browser tooltip

        // Add custom hover events for visual feedback without scaling
        markerElement.addEventListener('mouseenter', () => {
            markerElement.style.filter = 'brightness(1.2) drop-shadow(0 4px 8px rgba(26, 119, 62, 0.4))';
            markerElement.style.transition = 'filter 0.2s ease';
            markerElement.style.zIndex = '1000'; // Bring to front
        });

        markerElement.addEventListener('mouseleave', () => {
            markerElement.style.filter = 'none';
            markerElement.style.zIndex = 'auto';
        });
    }
};

const clearMapLayers = () => {
    if (!map.value || !map.value.isStyleLoaded()) return;

    // Remove existing GeoJSON layers and sources properly
    if (currentGeoJsonLayer.value) {
        // Remove outline layer first
        const outlineLayerId = `${currentGeoJsonLayer.value}-outline`;
        if (map.value.getLayer(outlineLayerId)) {
            map.value.removeLayer(outlineLayerId);
        }

        // Remove main layer
        if (map.value.getLayer(currentGeoJsonLayer.value)) {
            map.value.removeLayer(currentGeoJsonLayer.value);
        }

        // Remove source after all layers are removed
        if (map.value.getSource(currentGeoJsonLayer.value)) {
            map.value.removeSource(currentGeoJsonLayer.value);
        }

        currentGeoJsonLayer.value = null;
    }

    // Remove existing marker
    if (currentMarker.value) {
        currentMarker.value.remove();
        currentMarker.value = null;
    }

    // Remove click marker if exists
    if (clickMarker) {
        clickMarker.remove();
        clickMarker = null;
    }
};

const renderGeoJSON = (geojson, type) => {
    if (!map.value || !geojson) return;

    clearMapLayers();

    try {
        const geoJsonData = typeof geojson === 'string' ? JSON.parse(geojson) : geojson;
        const layerId = `geojson-layer-${Date.now()}`;

        // Add source
        map.value.addSource(layerId, {
            type: 'geojson',
            data: geoJsonData
        });

        // Get style based on type
        const style = getGeoJSONStyle(type);

        // Add fill layer
        map.value.addLayer({
            id: layerId,
            type: 'fill',
            source: layerId,
            paint: style.fill
        });

        // Add outline layer
        map.value.addLayer({
            id: `${layerId}-outline`,
            type: 'line',
            source: layerId,
            paint: style.outline
        });

        currentGeoJsonLayer.value = layerId;

        // Fit map to bounds with proper coordinate handling
        const bounds = new maplibregl.LngLatBounds();
        let hasValidCoords = false;

        const addCoordinates = (coords) => {
            if (Array.isArray(coords[0])) {
                coords.forEach(addCoordinates);
            } else if (coords.length >= 2) {
                // coords should be [longitude, latitude]
                bounds.extend([coords[0], coords[1]]);
                hasValidCoords = true;
            }
        };

        if (geoJsonData.geometry) {
            addCoordinates(geoJsonData.geometry.coordinates);
        }

        if (hasValidCoords && !bounds.isEmpty()) {
            map.value.fitBounds(bounds, { padding: 50 });
        }

    } catch (error) {
        console.error('Error rendering GeoJSON:', error);
    }
};

const getGeoJSONStyle = (type) => {
    const styles = {
        provinces: {
            fill: { 'fill-color': '#1A773E', 'fill-opacity': 0.3 },
            outline: { 'line-color': '#1A773E', 'line-width': 2 }
        },
        districts: {
            fill: { 'fill-color': '#1C5172', 'fill-opacity': 0.3 },
            outline: { 'line-color': '#1C5172', 'line-width': 2 }
        },
        sectors: {
            fill: { 'fill-color': '#303017', 'fill-opacity': 0.3 },
            outline: { 'line-color': '#303017', 'line-width': 2 }
        },
        cells: {
            fill: { 'fill-color': '#1A773E', 'fill-opacity': 0.2 },
            outline: { 'line-color': '#1A773E', 'line-width': 1 }
        },
        villages: {
            fill: { 'fill-color': '#1C5172', 'fill-opacity': 0.2 },
            outline: { 'line-color': '#1C5172', 'line-width': 1 }
        }
    };

    return styles[type] || styles.districts;
};

// Search functionality
const performSearch = debounce(async () => {
    // For text search, check minimum character requirement
    if (searchMode.value === 'text' && searchQuery.value.trim().length < 3) {
        clearSearch(false);
        return;
    }

    // For coordinate search, check if coordinates are valid
    if (searchMode.value === 'coordinates' && !canSearch.value) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    // Clear old data before rendering new data for coordinate search
    if (searchMode.value === 'coordinates') {
        clearMapLayers();
        coordinateSearchResults.value = [];
    }

    let query = searchMode.value === 'text'
        ? searchQuery.value.trim()
        : `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;

    try {
        // Use the same route for both text and coordinate searches
        const { data } = await axios.post('/gorilla/search', {
            searchQuery: query,
            lang: selectedLanguage.value,
            filterData: selectedFilter.value,
        });

        // Initialize results structure
        const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others'];
        searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));

        if (searchMode.value === 'coordinates') {
            // For coordinate search, handle the single result or array of results
            if (data && typeof data === 'object') {
                if (Array.isArray(data)) {
                    // If it's an array, use it directly
                    coordinateSearchResults.value = data.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }));
                } else if (data.key || data.name) {
                    // If it's a single result object, wrap it in an array
                    coordinateSearchResults.value = [{
                        ...data,
                        geojson: typeof data.geojson === 'string' ? JSON.parse(data.geojson) : data.geojson,
                        latitude: typeof data.latitude === 'string' ? parseFloat(data.latitude) : data.latitude,
                        longitude: typeof data.longitude === 'string' ? parseFloat(data.longitude) : data.longitude,
                    }];
                } else {
                    // Handle grouped results for coordinate search
                    for (const type of ALL_RESULT_TYPES) {
                        if (data[type] && Array.isArray(data[type])) {
                            searchResults.value[type] = data[type].map(item => ({
                                ...item,
                                type: item.type || type.slice(0, -1),
                                geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                                latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                                longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                            }));
                        }
                    }
                }

                lastSearchedCoords.value = {
                    latitude: coordinateForm.value.latitude,
                    longitude: coordinateForm.value.longitude
                };
            }
        } else {
            // For text search, handle grouped results
            for (const type of ALL_RESULT_TYPES) {
                if (data[type] && Array.isArray(data[type])) {
                    searchResults.value[type] = data[type].map(item => ({
                        ...item,
                        type: item.type || type.slice(0, -1), // Remove 's' from plural
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }));
                }
            }
        }

        searchTime.value = Math.round(performance.now() - startTime);

        // Handle coordinate search results
        if (searchMode.value === 'coordinates') {
            handleCoordinateSearchResults();
        }

    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Search failed. Please try again.';
        searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
        coordinateSearchResults.value = [];
    } finally {
        isLoading.value = false;
    }
}, 300);

const handleCoordinateSearchResults = () => {
    // First check coordinateSearchResults
    if (coordinateSearchResults.value && coordinateSearchResults.value.length > 0) {
        const foundResult = coordinateSearchResults.value[0];

        // Store selected location data for display
        selectedLocationData.value = { ...foundResult, type: foundResult.type || 'location' };

        // Always prioritize latitude/longitude for zooming to coordinate search results
        if (foundResult.latitude && foundResult.longitude) {
            const lat = parseFloat(foundResult.latitude);
            const lng = parseFloat(foundResult.longitude);

            if (!isNaN(lat) && !isNaN(lng)) {
                // Create enhanced popup for coordinate search result
                const popupContent = `
                    <div class="p-3 min-w-[200px]">
                        <h3 class="font-semibold text-gorilla-primary-three text-lg mb-2">${foundResult.name || 'Location Found'}</h3>
                        ${foundResult.address ? `<p class="text-sm text-gray-600 mb-2">${foundResult.address}</p>` : ''}
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span class="bg-gorilla-primary/10 text-gorilla-primary px-2 py-1 rounded-full">
                                ${foundResult.type || 'location'}
                            </span>
                            <span class="font-mono">
                                ${lat.toFixed(6)}, ${lng.toFixed(6)}
                            </span>
                        </div>
                    </div>
                `;

                // Create hover text for coordinate search result
                const hoverText = `${foundResult.name || 'Location Found'}${foundResult.address ? ' - ' + foundResult.address : ''} (${foundResult.type || 'location'})`;

                addMarker(lat, lng, popupContent, hoverText);

                // Zoom to the found location with appropriate level
                const zoomLevel = foundResult.type === 'village' ? 16 : 15;
                map.value.flyTo({
                    center: [lng, lat],
                    zoom: zoomLevel,
                    duration: 1200,
                    essential: true
                });

                // Render geojson if available after zooming
                if (foundResult.geojson) {
                    setTimeout(() => {
                        renderGeoJSON(foundResult.geojson, foundResult.type || 'location');
                    }, 500);
                }
                return;
            }
        }

        // Fallback to geojson only if coordinates are not available
        if (foundResult.geojson) {
            renderGeoJSON(foundResult.geojson, foundResult.type || 'location');
            return;
        }
    }

    // Then check regular search results for coordinate search
    let foundResult = null;
    const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'others'];

    for (const type of ALL_RESULT_TYPES) {
        if (searchResults.value[type] && searchResults.value[type].length > 0) {
            foundResult = searchResults.value[type][0];
            break;
        }
    }

    if (foundResult && foundResult.latitude && foundResult.longitude) {
        const lat = parseFloat(foundResult.latitude);
        const lng = parseFloat(foundResult.longitude);

        if (!isNaN(lat) && !isNaN(lng)) {
            const popupContent = `
                <div class="p-3">
                    <h3 class="font-semibold text-gorilla-primary-three">${foundResult.name || 'Location'}</h3>
                    ${foundResult.address ? `<p class="text-sm text-gray-600 mt-1">${foundResult.address}</p>` : ''}
                </div>
            `;

            const hoverText = `${foundResult.name || 'Location'}${foundResult.address ? ' - ' + foundResult.address : ''} (${foundResult.type || 'other'})`;

            addMarker(lat, lng, popupContent, hoverText);
            map.value.flyTo({ center: [lng, lat], zoom: 14, duration: 1200 });

            if (foundResult.geojson) {
                setTimeout(() => {
                    renderGeoJSON(foundResult.geojson, foundResult.type || 'other');
                }, 500);
            }
        }
    } else if (canSearch.value) {
        // No results found, show marker at searched coordinates
        const lat = parseFloat(coordinateForm.value.latitude);
        const lng = parseFloat(coordinateForm.value.longitude);

        if (!isNaN(lat) && !isNaN(lng)) {
            const popupContent = `
                <div class="p-3">
                    <h3 class="font-semibold text-red-600">No location data found</h3>
                    <p class="text-xs text-gray-500 mt-1 font-mono">${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
                </div>
            `;

            const hoverText = `No location data found at ${lat.toFixed(6)}, ${lng.toFixed(6)}`;

            addMarker(lat, lng, popupContent, hoverText);
            map.value.flyTo({ center: [lng, lat], zoom: 14, duration: 1200 });
        }
    }
};



const parseCoordinateInput = (input) => {
    const coordPattern = /^(-?\d+\.?\d*),\s*(-?\d+\.?\d*)$/;
    const match = input.match(coordPattern);

    if (match) {
        const lat = parseFloat(match[1]);
        const lng = parseFloat(match[2]);

        // Validate coordinate ranges
        if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
            return { latitude: lat, longitude: lng };
        }
    }
    return null;
};

const handleSearchModeChange = () => {
    localStorage.setItem('searchMode', searchMode.value);
    clearSearch();
    if (searchMode.value === 'coordinates') {
        searchQuery.value = '';
    } else {
        coordinateForm.value = { latitude: '', longitude: '' };
    }
};

const handleTextInput = (value) => {
    searchQuery.value = value;
    performSearch();
};

const handleCoordinateInput = (value) => {
    searchQuery.value = value;

    // Try to parse coordinate input like "-1.9441,30.0619"
    const coords = parseCoordinateInput(value);
    if (coords) {
        coordinateForm.value.latitude = coords.latitude.toString();
        coordinateForm.value.longitude = coords.longitude.toString();
        performSearch();
    }
};

const updateCoordinateSearch = () => {
    // Update the main search query when coordinates are entered manually
    if (coordinateForm.value.latitude && coordinateForm.value.longitude) {
        searchQuery.value = `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;
        performSearch();
    }
};

const submitCoordinateSearch = () => {
    if (canSearch.value) {
        performSearch();
    } else {
        const lat = parseFloat(coordinateForm.value.latitude);
        const lng = parseFloat(coordinateForm.value.longitude);

        if (isNaN(lat) || isNaN(lng)) {
            error.value = "Please enter valid latitude and longitude coordinates.";
        } else if (!isWithinRwandaBounds(lat, lng)) {
            error.value = "Coordinates must be within Rwanda boundaries.";
        } else {
            error.value = "Please enter valid coordinates.";
        }
        setTimeout(() => error.value = null, 3000);
    }
};

const clearSearch = (resetInputs = true) => {
    if (resetInputs) {
        searchQuery.value = '';
        coordinateForm.value.latitude = '';
        coordinateForm.value.longitude = '';
        lastSearchedCoords.value = null;
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    coordinateSearchResults.value = [];
    selectedLocationData.value = null; // Clear selected location data
    error.value = null;
    searchTime.value = 0;
    clearMapLayers();
};

const handleResultClick = (result, type) => {
    // Clear previous selections
    clearMapLayers();

    // Store selected location data for display
    selectedLocationData.value = { ...result, type: result.type || type };

    // Always prioritize latitude/longitude for zooming - this is the key functionality requested
    if (result.latitude && result.longitude) {
        const lat = parseFloat(result.latitude);
        const lng = parseFloat(result.longitude);

        // Validate coordinates
        if (isNaN(lat) || isNaN(lng)) {
            console.error('Invalid coordinates:', result.latitude, result.longitude);
            return;
        }

        // Create enhanced popup content
        const popupContent = `
            <div class="p-3 min-w-[200px]">
                <h3 class="font-semibold text-gorilla-primary-three text-lg mb-2">${result.name}</h3>
                ${result.address ? `<p class="text-sm text-gray-600 mb-2">${result.address}</p>` : ''}
                <div class="flex items-center justify-between text-xs text-gray-500">
                    <span class="bg-gorilla-primary/10 text-gorilla-primary px-2 py-1 rounded-full">
                        ${result.type || type}
                    </span>
                    <span class="font-mono">
                        ${lat.toFixed(6)}, ${lng.toFixed(6)}
                    </span>
                </div>
            </div>
        `;

        // Create hover text for the marker
        const hoverText = `${result.name}${result.address ? ' - ' + result.address : ''} (${result.type || type})`;

        // Add marker at the exact coordinates with hover text
        addMarker(lat, lng, popupContent, hoverText);

        // Determine appropriate zoom level based on location type for clean visibility
        let zoomLevel = 14; // Default zoom
        const locationTypes = {
            'province': 9,
            'district': 11,
            'sector': 13,
            'cell': 15,
            'village': 16,
            'location': 15,
            'other': 14
        };

        zoomLevel = locationTypes[result.type] || locationTypes[type] || 14;

        // Fly to the location with smooth animation
        map.value.flyTo({
            center: [lng, lat],
            zoom: zoomLevel,
            duration: 1200,
            essential: true
        });

        // If geojson is available, render it after zooming
        if (result.geojson) {
            setTimeout(() => {
                renderGeoJSON(result.geojson, result.type || type);
            }, 500);
        }
    } else if (result.geojson) {
        // Fallback to GeoJSON if no coordinates available
        renderGeoJSON(result.geojson, result.type || type);
    }

    // Store selected result key for highlighting
    if (result.key) {
        console.log('Selected result key:', result.key);
    }
};

const getDisplayName = (result) => {
    const langKey = `name_${selectedLanguage.value}`;
    return result[langKey] || result.name_en || result.name_local || result.name || 'N/A';
};

const getDisplayAddress = (result) => {
    // Try different address fields based on the result format
    return result.address ||
           result[`full_address_${selectedLanguage.value}`] ||
           result.full_address_en ||
           result.full_address_fr ||
           result.full_address_rw ||
           '';
};

const getUserLocation = () => {
    if (!navigator.geolocation) {
        error.value = "Geolocation is not supported by your browser.";
        return;
    }

    isLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (position) => {
            coordinateForm.value.latitude = position.coords.latitude.toFixed(6);
            coordinateForm.value.longitude = position.coords.longitude.toFixed(6);

            // Place custom marker at user location
            if (clickMarker) clickMarker.remove();
            clickMarker = new maplibregl.Marker({ color: '#1A773E' })
                .setLngLat([position.coords.longitude, position.coords.latitude])
                .addTo(map.value);

            // Add hover text to user location marker
            const markerElement = clickMarker.getElement();
            markerElement.title = `Your location: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`;

            // Add hover effects without scaling
            markerElement.addEventListener('mouseenter', () => {
                markerElement.style.filter = 'brightness(1.2) drop-shadow(0 4px 8px rgba(26, 119, 62, 0.4))';
                markerElement.style.transition = 'filter 0.2s ease';
                markerElement.style.zIndex = '1000';
            });

            markerElement.addEventListener('mouseleave', () => {
                markerElement.style.filter = 'none';
                markerElement.style.zIndex = 'auto';
            });

            submitCoordinateSearch();
        },
        (err) => {
            isLoading.value = false;
            error.value = `Failed to get location: ${err.message}`;
            setTimeout(() => error.value = null, 3000);
        }
    );
};

// --- LIFECYCLE HOOKS ---
onMounted(() => {
    nextTick(() => {
        initializeMap();
    });
});

onUnmounted(() => {
    if (map.value) {
        map.value.remove();
    }
});

// --- WATCHERS ---
watch(mapTheme, () => {
    if (map.value && map.value.isStyleLoaded()) {
        const newStyle = getMapStyle();
        map.value.setStyle(newStyle);
    }
});

watch(searchMode, () => {
    handleSearchModeChange();
});

watch([() => coordinateForm.value.latitude, () => coordinateForm.value.longitude], () => {
    if (searchMode.value === 'coordinates' && canSearch.value) {
        // Update the main search query when coordinates change
        searchQuery.value = `${coordinateForm.value.latitude},${coordinateForm.value.longitude}`;
    }
}, { deep: true });

// Watch for filter and language changes to re-search
watch([selectedLanguage, selectedFilter], () => {
    // Only re-search if there's an active search query
    if (searchQuery.value.trim().length >= 3 || (searchMode.value === 'coordinates' && canSearch.value)) {
        performSearch();
    }
});

// Helper function to handle recommended search clicks
const handleRecommendedSearch = (searchTerm) => {
    console.log('Recommended search clicked:', searchTerm);

    // Clear any existing results first
    clearSearch(false);

    // Ensure we're in text mode first
    if (searchMode.value !== 'text') {
        searchMode.value = 'text';
        handleSearchModeChange();
    }

    // Set the search query and trigger search using the existing handleTextInput function
    handleTextInput(searchTerm);
};
</script>

<template>
    <AppLayout title="Rwanda Geo Search">
        <div class="min-h-screen bg-cta-background-two">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-6 lg:px-8">
                    <div class="py-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-3xl font-bold text-gorilla-primary-three">
                                    Rwanda Geo Search
                                </h1>
                                <p class="mt-2 text-gray-600">
                                    Search locations by name or coordinates with interactive map visualization
                                </p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <!-- Map Theme Selector -->
                                <Select v-model="mapTheme">
                                    <SelectTrigger class="w-32 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                                        <Layers class="mr-2 h-4 w-4" />
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem
                                            v-for="theme in mapThemes"
                                            :key="theme.value"
                                            :value="theme.value"
                                        >
                                            {{ theme.label }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="max-w-7xl mx-auto px-6 lg:px-8 py-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Search Panel -->
                    <div class="lg:col-span-1 space-y-6">
                        <!-- Search Mode Toggle -->
                        <Card>
                            <CardContent class="pt-6">
                                <!-- Minimal Toggle Switch -->
                                <div class="flex items-center justify-center">
                                    <div class="bg-cta-background-one rounded-full p-1 flex items-center">
                                        <button
                                            @click="searchMode = 'text'; handleSearchModeChange()"
                                            :class="[
                                                'px-4 py-2 text-sm font-medium transition-all duration-200 flex items-center gap-1.5 rounded-full',
                                                searchMode === 'text'
                                                    ? 'bg-gorilla-primary text-white shadow-sm'
                                                    : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'
                                            ]"
                                        >
                                            <Globe class="w-3.5 h-3.5" />
                                            Text
                                        </button>
                                        <button
                                            @click="searchMode = 'coordinates'; handleSearchModeChange()"
                                            :class="[
                                                'px-4 py-2 text-sm font-medium transition-all duration-200 flex items-center gap-1.5 rounded-full',
                                                searchMode === 'coordinates'
                                                    ? 'bg-gorilla-primary text-white shadow-sm'
                                                    : 'text-gorilla-primary-three hover:bg-white hover:text-gorilla-primary'
                                            ]"
                                        >
                                            <Crosshair class="w-3.5 h-3.5" />
                                            Coords
                                        </button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- Search Input -->
                        <Card>
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center justify-between">
                                    <span>Search Input</span>
                                    <!-- Small Clean Button -->
                                    <Button
                                        @click="clearSearch"
                                        variant="ghost"
                                        size="sm"
                                        class="h-8 w-8 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50"
                                        title="Clear search"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </Button>
                                </CardTitle>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <!-- Text Search Input -->
                                <div v-if="searchMode === 'text'" class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search class="h-5 w-5 text-gray-400" />
                                    </div>
                                    <Input
                                        v-model="searchQuery"
                                        @input="handleTextInput($event.target.value)"
                                        :placeholder="getPlaceholderText"
                                        class="pl-10 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                                    />
                                    <div v-if="isLoading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <Loader2 class="h-5 w-5 text-gorilla-primary animate-spin" />
                                    </div>
                                </div>

                                <!-- Coordinate Search Input -->
                                <div v-else class="space-y-4">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <Navigation class="h-5 w-5 text-gray-400" />
                                        </div>
                                        <Input
                                            v-model="searchQuery"
                                            @input="handleCoordinateInput($event.target.value)"
                                            :placeholder="getPlaceholderText"
                                            class="pl-10 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20"
                                        />
                                        <div v-if="isLoading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                            <Loader2 class="h-5 w-5 text-gorilla-primary animate-spin" />
                                        </div>
                                    </div>
                                    <div class="text-center text-sm text-gray-500">or enter coordinates separately below</div>

                                    <!-- Coordinate Form -->
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="space-y-2">
                                            <Label class="text-sm font-semibold text-gorilla-primary-three">Latitude</Label>
                                            <Input
                                                v-model="coordinateForm.latitude"
                                                @input="updateCoordinateSearch"
                                                type="number"
                                                step="any"
                                                placeholder="-1.9403"
                                                class="py-3 px-4 border border-gray-300 rounded-xl focus:ring-0 focus:border-gray-400 font-medium transition-all duration-200"
                                                @keyup.enter="submitCoordinateSearch"
                                            />
                                        </div>
                                        <div class="space-y-2">
                                            <Label class="text-sm font-semibold text-gorilla-primary-three">Longitude</Label>
                                            <Input
                                                v-model="coordinateForm.longitude"
                                                @input="updateCoordinateSearch"
                                                type="number"
                                                step="any"
                                                placeholder="29.8739"
                                                class="py-3 px-4 border border-gray-300 rounded-xl focus:ring-0 focus:border-gray-400 font-medium transition-all duration-200"
                                                @keyup.enter="submitCoordinateSearch"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <!-- Filters Toggle -->
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <Label class="text-sm font-medium text-gray-700">Filters & Language</Label>
                                        <Button
                                            @click="showFilters = !showFilters"
                                            variant="ghost"
                                            size="sm"
                                            class="text-xs h-6 px-2"
                                        >
                                            {{ showFilters ? 'Hide' : 'Show' }} Filters
                                            <Layers class="ml-1 h-3 w-3" />
                                        </Button>
                                    </div>

                                    <!-- Filter Selection and Language (when filters are shown) -->
                                    <div v-if="showFilters" class="transition-all duration-200 space-y-3">
                                        <div>
                                            <Label class="text-sm font-medium text-gray-700">Language</Label>
                                            <Select v-model="selectedLanguage">
                                                <SelectTrigger class="mt-1 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem
                                                        v-for="lang in languages"
                                                        :key="lang.code"
                                                        :value="lang.code"
                                                    >
                                                        {{ lang.flag }} {{ lang.name }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div v-if="searchMode === 'text'">
                                            <Label class="text-sm font-medium text-gray-700">Location Type</Label>
                                            <Select v-model="selectedFilter">
                                                <SelectTrigger class="mt-1 border-gray-300 focus:border-gorilla-primary focus:ring-gorilla-primary/20">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem
                                                        v-for="filter in filters"
                                                        :key="filter.value"
                                                        :value="filter.value"
                                                    >
                                                        {{ filter.label }}
                                                    </SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div v-if="searchMode === 'coordinates'" class="flex justify-center">
                                    <Button
                                        @click="getUserLocation"
                                        variant="outline"
                                        class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white"
                                        :disabled="isLoading"
                                    >
                                        <LocateFixed class="mr-2 h-4 w-4" />
                                        My Location
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- Recommended Searches -->
                        <Card v-if="!hasResults && !isLoading && searchQuery.trim().length < 3">
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center">
                                    <Search class="mr-2 h-5 w-5" />
                                    Try These Searches
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-2">
                                    <p class="text-sm text-gray-600 mb-3">Popular location searches in Rwanda:</p>
                                    <div class="flex flex-wrap gap-2">
                                        <button
                                            v-for="search in recommendedSearches"
                                            :key="search"
                                            @click.prevent="handleRecommendedSearch(search)"
                                            class="px-3 py-2 text-sm bg-cta-background-one text-gorilla-primary-three rounded-lg hover:bg-gorilla-primary hover:text-white transition-all duration-200 border border-gray-200 hover:border-gorilla-primary cursor-pointer"
                                        >
                                            {{ search }}
                                        </button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- Search Results -->
                        <Card v-if="hasResults || isLoading || error">
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center justify-between">
                                    <div class="flex items-center">
                                        <Building class="mr-2 h-5 w-5" />
                                        Search Results
                                    </div>
                                    <div class="flex items-center space-x-2 text-sm">
                                        <span v-if="isLoading" class="text-gray-500 flex items-center">
                                            <Loader2 class="mr-1 h-3 w-3 animate-spin" />
                                            Searching...
                                        </span>
                                        <span v-else-if="error" class="text-red-600 flex items-center">
                                            <AlertCircle class="mr-1 h-3 w-3" />
                                            Error
                                        </span>
                                        <span v-else class="text-gray-600">
                                            {{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }}
                                        </span>
                                        <span v-if="searchTime > 0 && !isLoading" class="text-xs bg-gorilla-primary/10 text-gorilla-primary px-2 py-1 rounded-full flex items-center">
                                            <Clock class="mr-1 h-3 w-3" />
                                            {{ searchTime }}ms
                                        </span>
                                    </div>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <!-- Error Message -->
                                <div v-if="error" class="p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="flex items-center">
                                        <AlertCircle class="h-5 w-5 text-red-600 mr-2" />
                                        <span class="text-red-800">{{ error }}</span>
                                    </div>
                                </div>

                                <!-- Loading State -->
                                <div v-else-if="isLoading" class="p-8 text-center">
                                    <Loader2 class="h-8 w-8 text-gorilla-primary animate-spin mx-auto mb-2" />
                                    <p class="text-gray-600">Searching Rwanda locations...</p>
                                </div>

                                <!-- Text Search Results List -->
                                <div v-else-if="searchMode === 'text' && hasResults" class="max-h-96 overflow-y-auto space-y-2">
                                    <template v-for="(resultArray, type) in searchResults" :key="type">
                                        <div v-if="resultArray && resultArray.length > 0">
                                            <!-- Type Header -->
                                            <div class="sticky top-0 bg-cta-background-one px-3 py-2 rounded-lg mb-2">
                                                <h4 class="text-sm font-semibold text-gorilla-primary-three capitalize">
                                                    {{ type }} ({{ resultArray.length }})
                                                </h4>
                                            </div>

                                            <!-- Results -->
                                            <div class="space-y-1">
                                                <div
                                                    v-for="result in resultArray"
                                                    :key="`${type}-${result.key || result.id}`"
                                                    @click="handleResultClick(result, type)"
                                                    class="p-3 rounded-lg border border-gray-200 hover:border-gorilla-primary hover:bg-gorilla-primary/5 cursor-pointer transition-all duration-200"
                                                >
                                                    <div class="flex items-start justify-between">
                                                        <div class="flex-1 min-w-0">
                                                            <h5 class="font-medium text-gorilla-primary-three truncate">
                                                                {{ getDisplayName(result) }}
                                                            </h5>
                                                            <p v-if="getDisplayAddress(result)" class="text-sm text-gray-600 truncate mt-1">
                                                                {{ getDisplayAddress(result) }}
                                                            </p>
                                                            <div class="flex items-center mt-2 space-x-2">
                                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gorilla-primary/10 text-gorilla-primary">
                                                                    {{ result.type || type.slice(0, -1) }}
                                                                </span>
                                                                <span v-if="result.latitude && result.longitude" class="text-xs text-gray-500">
                                                                    {{ parseFloat(result.latitude).toFixed(4) }}, {{ parseFloat(result.longitude).toFixed(4) }}
                                                                </span>
                                                                <span v-if="result.key" class="text-xs text-gray-400 font-mono">
                                                                    {{ result.key.substring(0, 8) }}...
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-3 flex-shrink-0">
                                                            <MapPin class="h-5 w-5 text-gorilla-primary" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <!-- Coordinate Search Results List -->
                                <div v-else-if="searchMode === 'coordinates' && (coordinateSearchResults.length > 0 || hasResults)" class="max-h-96 overflow-y-auto space-y-2">
                                    <!-- Direct coordinate results -->
                                    <div v-if="coordinateSearchResults.length > 0" class="space-y-1">
                                        <div class="sticky top-0 bg-cta-background-one px-3 py-2 rounded-lg mb-2">
                                            <h4 class="text-sm font-semibold text-gorilla-primary-three">
                                                Location Found ({{ coordinateSearchResults.length }})
                                            </h4>
                                        </div>
                                        <div
                                            v-for="result in coordinateSearchResults"
                                            :key="`coord-${result.key || result.id}`"
                                            @click="handleResultClick(result, 'location')"
                                            class="p-3 rounded-lg border border-gray-200 hover:border-gorilla-primary hover:bg-gorilla-primary/5 cursor-pointer transition-all duration-200"
                                        >
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1 min-w-0">
                                                    <h5 class="font-medium text-gorilla-primary-three truncate">
                                                        {{ getDisplayName(result) }}
                                                    </h5>
                                                    <p v-if="getDisplayAddress(result)" class="text-sm text-gray-600 truncate mt-1">
                                                        {{ getDisplayAddress(result) }}
                                                    </p>
                                                    <div class="flex items-center mt-2 space-x-2">
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gorilla-primary/10 text-gorilla-primary">
                                                            {{ result.type || 'location' }}
                                                        </span>
                                                        <span v-if="result.latitude && result.longitude" class="text-xs text-gray-500">
                                                            {{ parseFloat(result.latitude).toFixed(4) }}, {{ parseFloat(result.longitude).toFixed(4) }}
                                                        </span>
                                                        <span v-if="result.key" class="text-xs text-gray-400 font-mono">
                                                            {{ result.key.substring(0, 8) }}...
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-3 flex-shrink-0">
                                                    <MapPin class="h-5 w-5 text-gorilla-primary" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Fallback to regular search results for coordinates -->
                                    <template v-else-if="hasResults" v-for="(resultArray, type) in searchResults" :key="type">
                                        <div v-if="resultArray && resultArray.length > 0">
                                            <div class="sticky top-0 bg-cta-background-one px-3 py-2 rounded-lg mb-2">
                                                <h4 class="text-sm font-semibold text-gorilla-primary-three capitalize">
                                                    {{ type }} ({{ resultArray.length }})
                                                </h4>
                                            </div>
                                            <div class="space-y-1">
                                                <div
                                                    v-for="result in resultArray"
                                                    :key="`${type}-${result.key || result.id}`"
                                                    @click="handleResultClick(result, type)"
                                                    class="p-3 rounded-lg border border-gray-200 hover:border-gorilla-primary hover:bg-gorilla-primary/5 cursor-pointer transition-all duration-200"
                                                >
                                                    <div class="flex items-start justify-between">
                                                        <div class="flex-1 min-w-0">
                                                            <h5 class="font-medium text-gorilla-primary-three truncate">
                                                                {{ getDisplayName(result) }}
                                                            </h5>
                                                            <p v-if="getDisplayAddress(result)" class="text-sm text-gray-600 truncate mt-1">
                                                                {{ getDisplayAddress(result) }}
                                                            </p>
                                                            <div class="flex items-center mt-2 space-x-2">
                                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gorilla-primary/10 text-gorilla-primary">
                                                                    {{ result.type || type.slice(0, -1) }}
                                                                </span>
                                                                <span v-if="result.latitude && result.longitude" class="text-xs text-gray-500">
                                                                    {{ parseFloat(result.latitude).toFixed(4) }}, {{ parseFloat(result.longitude).toFixed(4) }}
                                                                </span>
                                                                <span v-if="result.key" class="text-xs text-gray-400 font-mono">
                                                                    {{ result.key.substring(0, 8) }}...
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-3 flex-shrink-0">
                                                            <MapPin class="h-5 w-5 text-gorilla-primary" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <!-- No Results -->
                                <div v-else class="p-8 text-center">
                                    <Search class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <p class="text-gray-600">No locations found</p>
                                    <p class="text-sm text-gray-500 mt-1">Try adjusting your search terms or filters</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <!-- Map Panel -->
                    <div class="lg:col-span-2 space-y-6">
                        <Card class="h-auto">
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center justify-between">
                                    <div class="flex items-center">
                                        <Globe class="mr-2 h-5 w-5" />
                                        Interactive Map
                                    </div>
                                    <div v-if="searchMode === 'coordinates'" class="text-sm text-gray-600 flex items-center">
                                        <Crosshair class="mr-1 h-4 w-4" />
                                        Click to search coordinates
                                    </div>
                                </CardTitle>
                            </CardHeader>
                            <CardContent class="p-0">
                                <div
                                    ref="mapContainer"
                                    class="w-full h-[500px] rounded-b-lg"
                                ></div>
                            </CardContent>
                        </Card>

                        <!-- Selected Location Details Card -->
                        <Card v-if="selectedLocationData" class="border-gorilla-primary/20">
                            <CardHeader>
                                <CardTitle class="text-gorilla-primary flex items-center">
                                    <MapPin class="mr-2 h-5 w-5" />
                                    Selected Location
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-3">
                                    <div>
                                        <h3 class="font-semibold text-gorilla-primary-three text-lg">
                                            {{ getDisplayName(selectedLocationData) }}
                                        </h3>
                                        <p v-if="getDisplayAddress(selectedLocationData)" class="text-gray-600 mt-1">
                                            {{ getDisplayAddress(selectedLocationData) }}
                                        </p>
                                    </div>

                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div v-if="selectedLocationData.type" class="flex items-center">
                                            <span class="font-medium text-gray-700 mr-2">Type:</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gorilla-primary/10 text-gorilla-primary">
                                                {{ selectedLocationData.type }}
                                            </span>
                                        </div>
                                        <div v-if="selectedLocationData.latitude && selectedLocationData.longitude" class="flex items-center">
                                            <span class="font-medium text-gray-700 mr-2">Coordinates:</span>
                                            <span class="font-mono text-gray-600">
                                                {{ parseFloat(selectedLocationData.latitude).toFixed(6) }}, {{ parseFloat(selectedLocationData.longitude).toFixed(6) }}
                                            </span>
                                        </div>
                                        <div v-if="selectedLocationData.key" class="flex items-center">
                                            <span class="font-medium text-gray-700 mr-2">ID:</span>
                                            <span class="font-mono text-gray-500 text-xs">
                                                {{ selectedLocationData.key }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* MapLibre GL CSS is imported globally */

/* Custom scrollbar for results */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Smooth transitions */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Map container styling */
.maplibregl-map {
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Custom marker styling */
.maplibregl-marker {
    cursor: pointer;
}

/* Loading animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Result item hover effects */
.result-item {
    transition: all 0.2s ease-in-out;
}

.result-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(26, 119, 62, 0.1);
}

/* Map theme transition */
.maplibregl-canvas {
    transition: opacity 0.3s ease-in-out;
}

/* Custom focus styles for accessibility */
.focus\:ring-gorilla-primary\/20:focus {
    --tw-ring-color: rgba(26, 119, 62, 0.2);
}

/* Sticky header in results */
.sticky {
    position: sticky;
    z-index: 10;
}

/* Custom badge styling */
.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
}

/* Map controls positioning */
.maplibregl-ctrl-top-right {
    top: 10px;
    right: 10px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .maplibregl-map {
        height: 400px !important;
    }
}

@media (max-width: 768px) {
    .maplibregl-map {
        height: 300px !important;
    }
}
</style>
