<script setup>
import { Head, <PERSON>, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { User, Mail, Lock, Eye, EyeOff, ArrowRight, Key, Shield } from 'lucide-vue-next';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    terms: false,
    invitation_code: '',
});

const showPassword = ref(false);
const showConfirmPassword = ref(false);

const submit = () => {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};

// Form completion progress
const getFormProgress = () => {
    const fields = [form.name, form.email, form.password, form.password_confirmation, form.invitation_code];
    const filledFields = fields.filter(field => field && field.trim() !== '').length;
    const termsChecked = form.terms ? 1 : 0;
    return Math.round(((filledFields + termsChecked) / 6) * 100);
};

// Password strength indicator
const getPasswordStrength = (password) => {
    if (!password) return { strength: 0, label: '', color: '' };

    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500'];

    return {
        strength,
        label: labels[strength - 1] || '',
        color: colors[strength - 1] || 'bg-gray-300'
    };
};
</script>

<template>
    <Head title="Join OnRwanda Geo" />

    <div class="min-h-screen bg-gradient-to-br from-cta-background-two via-white to-cta-background-one flex items-center justify-center p-4">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23303017&quot; fill-opacity=&quot;0.1&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;2&quot;/%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>

        <div class="relative w-full max-w-lg">
            <!-- Logo Section -->
            <div class="text-center mb-8">
                <Link :href="'/'" class="inline-block">
                    <div class="bg-white rounded-2xl p-4 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <img src="/OnRwandaBlackNoIconLogo.png" alt="OnRwanda Geo" class="h-12 w-auto mx-auto"/>
                    </div>
                </Link>
                <h1 class="mt-6 text-3xl font-bold text-gorilla-primary-three">
                    Join OnRwanda Geo
                </h1>
                <p class="mt-2 text-gray-600">
                    Start exploring Rwanda's geography with precision
                </p>
            </div>

            <!-- Registration Card -->
            <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Name Field -->
                    <div class="space-y-2">
                        <label for="name" class="block text-sm font-semibold text-gorilla-primary-three">
                            Full Name
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <User class="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                id="name"
                                v-model="form.name"
                                type="text"
                                class="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:border-gorilla-primary focus:ring-4 focus:ring-gorilla-primary/10 transition-all duration-200 text-gorilla-primary-three placeholder-gray-400"
                                placeholder="Enter your full name"
                                required
                                autofocus
                                autocomplete="name"
                            />
                        </div>
                        <div v-if="form.errors.name" class="text-red-600 text-sm mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                            {{ form.errors.name }}
                        </div>
                    </div>

                    <!-- Email Field -->
                    <div class="space-y-2">
                        <label for="email" class="block text-sm font-semibold text-gorilla-primary-three">
                            Email Address
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <Mail class="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                id="email"
                                v-model="form.email"
                                type="email"
                                class="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:border-gorilla-primary focus:ring-4 focus:ring-gorilla-primary/10 transition-all duration-200 text-gorilla-primary-three placeholder-gray-400"
                                placeholder="Enter your email address"
                                required
                                autocomplete="username"
                            />
                        </div>
                        <div v-if="form.errors.email" class="text-red-600 text-sm mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                            {{ form.errors.email }}
                        </div>
                    </div>

                    <!-- Password Field -->
                    <div class="space-y-2">
                        <label for="password" class="block text-sm font-semibold text-gorilla-primary-three">
                            Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <Lock class="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                id="password"
                                v-model="form.password"
                                :type="showPassword ? 'text' : 'password'"
                                class="w-full pl-12 pr-12 py-4 border border-gray-300 rounded-xl focus:border-gorilla-primary focus:ring-4 focus:ring-gorilla-primary/10 transition-all duration-200 text-gorilla-primary-three placeholder-gray-400"
                                placeholder="Create a strong password"
                                required
                                autocomplete="new-password"
                            />
                            <button
                                type="button"
                                @click="showPassword = !showPassword"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gorilla-primary transition-colors"
                            >
                                <Eye v-if="!showPassword" class="h-5 w-5" />
                                <EyeOff v-else class="h-5 w-5" />
                            </button>
                        </div>
                        <!-- Password Strength Indicator -->
                        <div v-if="form.password" class="mt-2">
                            <div class="flex items-center space-x-2">
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div
                                        :class="[getPasswordStrength(form.password).color, 'h-2 rounded-full transition-all duration-300']"
                                        :style="{ width: `${(getPasswordStrength(form.password).strength / 5) * 100}%` }"
                                    ></div>
                                </div>
                                <span class="text-xs font-medium text-gray-600">
                                    {{ getPasswordStrength(form.password).label }}
                                </span>
                            </div>
                        </div>
                        <div v-if="form.errors.password" class="text-red-600 text-sm mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                            {{ form.errors.password }}
                        </div>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="space-y-2">
                        <label for="password_confirmation" class="block text-sm font-semibold text-gorilla-primary-three">
                            Confirm Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <Shield class="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                :type="showConfirmPassword ? 'text' : 'password'"
                                class="w-full pl-12 pr-12 py-4 border border-gray-300 rounded-xl focus:border-gorilla-primary focus:ring-4 focus:ring-gorilla-primary/10 transition-all duration-200 text-gorilla-primary-three placeholder-gray-400"
                                placeholder="Confirm your password"
                                required
                                autocomplete="new-password"
                            />
                            <button
                                type="button"
                                @click="showConfirmPassword = !showConfirmPassword"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gorilla-primary transition-colors"
                            >
                                <Eye v-if="!showConfirmPassword" class="h-5 w-5" />
                                <EyeOff v-else class="h-5 w-5" />
                            </button>
                        </div>
                        <div v-if="form.errors.password_confirmation" class="text-red-600 text-sm mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                            {{ form.errors.password_confirmation }}
                        </div>
                    </div>

                    <!-- Invitation Code Field -->
                    <div class="space-y-2">
                        <label for="invitation_code" class="block text-sm font-semibold text-gorilla-primary-three">
                            Invitation Code
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <Key class="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                id="invitation_code"
                                v-model="form.invitation_code"
                                type="text"
                                class="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:border-gorilla-primary focus:ring-4 focus:ring-gorilla-primary/10 transition-all duration-200 text-gorilla-primary-three placeholder-gray-400"
                                placeholder="Enter your invitation code"
                                required
                                autocomplete="invitation_code"
                            />
                        </div>
                        <div v-if="form.errors.invitation_code" class="text-red-600 text-sm mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                            {{ form.errors.invitation_code }}
                        </div>
                    </div>

                    <!-- Terms and Privacy Policy -->
                    <div v-if="$page.props.jetstream.hasTermsAndPrivacyPolicyFeature" class="space-y-2">
                        <label class="flex items-start cursor-pointer group">
                            <input
                                id="terms"
                                v-model="form.terms"
                                type="checkbox"
                                class="w-4 h-4 text-gorilla-primary border-gray-300 rounded focus:ring-gorilla-primary/20 focus:ring-2 mt-1"
                                required
                            />
                            <div class="ml-3 text-sm text-gray-600 group-hover:text-gorilla-primary-three transition-colors">
                                I agree to the
                                <a
                                    target="_blank"
                                    :href="route('terms.show')"
                                    class="text-gorilla-primary hover:text-gorilla-primary/80 font-medium underline"
                                >
                                    Terms of Service
                                </a>
                                and
                                <a
                                    target="_blank"
                                    :href="route('policy.show')"
                                    class="text-gorilla-primary hover:text-gorilla-primary/80 font-medium underline"
                                >
                                    Privacy Policy
                                </a>
                            </div>
                        </label>
                        <div v-if="form.errors.terms" class="text-red-600 text-sm mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                            {{ form.errors.terms }}
                        </div>
                    </div>

                    <!-- Register Button -->
                    <button
                        type="submit"
                        :disabled="form.processing"
                        class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
                    >
                        <span v-if="!form.processing">Create Account</span>
                        <span v-else>Creating your account...</span>
                        <ArrowRight v-if="!form.processing" class="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                        <div v-else class="ml-2 animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    </button>

                    <!-- Login Link -->
                    <div class="text-center pt-4 border-t border-gray-100">
                        <p class="text-gray-600 text-sm">
                            Already have an account?
                            <Link
                                :href="route('login')"
                                class="text-gorilla-primary hover:text-gorilla-primary/80 font-semibold ml-1 transition-colors"
                            >
                                Sign in
                            </Link>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center mt-8">
                <p class="text-xs text-gray-500">
                    Join thousands exploring Rwanda's geography
                </p>
            </div>
        </div>
    </div>
</template>
